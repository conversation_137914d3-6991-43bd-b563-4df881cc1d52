#include <iostream>
using namespace std;
// check palindrome or not
char tolower(char ch)
{
    if (ch >= 'a' && ch <= 'z')
    {
        return ch;
    }
    else
    {
        char temp = ch - 'A' + 'a';
        return temp;
    }
}
bool palindrome(string num)
{
    int start = 0;
    int end = num.length() - 1;
    while (start <= end)
    {
        if (num[start] != num[end])
        {
            return 0;
        }
        else
        {
            start++;
            end--;
        }
    }
    return 1;
}
bool valid(char ch)
{
    return  ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || (ch >= '0' && ch <= '9'));
}
int main()
{
    string s = "A man, a plan, a canal: Panama";

    string temp;
    for (int i = 0; i < s.length(); i++)
    {
        if (valid(s[i]))
        {
            temp.push_back(tolower(s[i]));
        }
        else
        {
            return false;
        }
         if(palindrome(temp))
    {
        cout << "true";
    }
    else
    {
        cout << "false";
    }
    }
   
   
}