#include<iostream>
using namespace std;

int power(int n, int pod){
    if(pod == 0){
        return 1; // Base case: any number to the power 0 is 1
    }
    int smallAns = power(n, pod - 1); // Recursive call
    return n * smallAns; // Multiply with base
}

int main(){
    int n ;
    cout <<"Enter a value of n: ";
    cin >> n ;
    
    int pod;
    cout<<"Enter value of power: ";
    cin >> pod;

    int ans = power(n, pod);
    cout << "Result: " << ans << endl;
    return 0;
}
