#include<iostream>
using namespace std;
// int main(){
//     int n ;
//     cin>>n;
//     int start=0;
//     int end=1;
//     int next=start+end;
//     for(int i = 0 ; i<n; i++){
//         cout << start << endl;
//         start=end;
//         end=next;
//         next=start+end;
//     }
//     return next; 
// }

// create recursively
int check(int n ){
    //create a base
    if(n==0)
     return 0 ;
     if(n==1)
        return 1;
     return check(n-1)+ check(n-2);
}
int main(){
    int n;
    cin>>n;
   int ans =  check(n);
   cout << ans << endl ;
    return 0;
}