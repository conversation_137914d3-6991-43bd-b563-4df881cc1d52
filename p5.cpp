#include<iostream>
using namespace std;

int check(int num[],int n){
     int start = 0 ; 
     int end = n-1;
     int mid = start+(end-start)/2;
     while(start < end ){
          if(num[mid]<num[mid+1]){
               start = mid+1;
          }else {
                end = mid;
          }
          mid = start +(end-start)/2;
     }
  return start;
}
int main(){
     int num[4]={3,4,5,1};

     cout << "The high value is : " << check(num,4) << endl  ;


}