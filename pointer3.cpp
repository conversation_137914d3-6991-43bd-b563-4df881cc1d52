#include<iostream>
using namespace std;
int main(){
    int n =5;
    int *p1 = &n;
    int **p2 = &p1;

    // cout << &n << endl ; 
    // cout << p1 << endl ;
    // cout << *p2 << endl ;
    cout <<"Before"  << n << endl ;
    cout <<"Before"  << p1 << endl ;
    cout <<"Before"  << p2 << endl ;


      **p2 = **p2+1;
      cout<<"After"  << n << endl ; 
      cout<<"After"  << p1 << endl ; 
      cout<<"After"  << p2 << endl ; 
    return 0 ; 
}