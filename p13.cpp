#include<iostream>
#include<algorithm>
using  namespace std ; 
int merge(int n[],int a,int n1[],int b){
      for(int i =0 ; i<b;i++){
           n[a]=n1[i];
           a++;
      } 
    sort(n,n+a+b);
}
void show(int n[],int a){
    for(int i =0 ; i<a;i++){
        cout<<n[i]<<" ";
    }
    
}
int main(){
    int n[6]={1,2,3,0,0,0};
    int n1[3] = {2,4,6};
    merge(n,3,n1,3);
    show(n,6);
   

}