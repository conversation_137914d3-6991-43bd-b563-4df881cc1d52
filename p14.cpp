#include<iostream>
using namespace std;

void check(int num[], int n, int k) {
    // Adjust k if it's larger than n
    k = k % n;  //

    // Print last k elements
    for (int i = n - k; i < n; i++) {
        cout << num[i] << " ";
    }

    // Print first n-k elements
    for (int i = 0; i < n - k; i++) {
        cout << num[i] << " ";
    }

    cout << endl;
}

int main() {
    int n[5] = {1, 2, 3, 4, 5};
    int k = 2;

    // Expected output: 4 5 1 2 3
    check(n, 5, k);
    return 0;
}
