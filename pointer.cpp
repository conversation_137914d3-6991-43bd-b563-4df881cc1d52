#include<iostream>
using namespace std;
int main(){
    // int  n =5;
    
    // int *p = &n;
    // cout << "The value of p is " << *p << endl ;
    // cout << "The address of p is " << p << endl ;
    // cout << "The address of n is " << &n << endl;

    // //copy a pointer

    // int *q = p ;
    // cout << "The adress of p and q is" << p << " - " << *q << endl ;
    // cout << "The value of p and q is" << *p << " - " << *q << endl ;
    
    // int s = 10 ; 
    // int *t = &s;
    // // cout << "The value of t is" << *t << endl ;
    //  cout << "Before The value of t is " << t  << endl ;
    //  cout << "Before The value of t is " << *t  << endl ;


    //  t=t+1;
    //  cout << " AfterThe value of t is " << t  << endl ;
    //  cout << "After the value of t is " << *t  << endl ;
    // int a = 20 ;
    // int *b= &a;
    // cout << "The value of a is" << b << endl ;

    // int arr[10]={1,5,55,4,5,6,7,8,9,10};
    // int *c = arr;
    // // cout << "The value of arr is   " << c << endl ;
    // cout << "The element of arr is " << *c << endl ;
    // cout << "The element of arr method is" << arr << endl ;
    // cout << "The new one is " <<(*c)+1 << endl ;
    // int i =3;
    // cout << " The value is "  << i[arr] << endl ;

    // // for (int i = 0 ; i < 10 ; i++){
    // //       cout << "The value is " << *c  << endl;
    // //       c++;
    // // }
    // return 0 ; 
    int arr[4]={1,2};
    int *ptr = arr;
    // arr= arr+1;                      It shows an error
    cout << "The values " << ptr << endl; 
     cout << "Next -->" <<endl;
    cout << "The values " << ptr+1 << endl;  //the address value hasbeen increased

    // int *p = &arr[0];
    // cout << "1st" << &arr[4] << endl ;
 }