#include <iostream>
using namespace std;

int main()
{
    int n;
    cout << "Enter a value of n " << endl;
    cin >> n;
    int **arr = new int *[n];
    // create a row
    for (int i = 0; i < n; i++)
    {
        arr[i] = new int[n];
    }

    // take a input
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < n; j++)
        {
            cin >> arr[i][j];
        }
    }

    // show a output
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < n; j++)
        {
            cout << arr[i][j] << ' ';
        }
        cout << endl;
    }

    //release memory 

    for(int i = 0 ; i<n ; i++){
        delete []arr[i];
    }
    delete []arr;
}