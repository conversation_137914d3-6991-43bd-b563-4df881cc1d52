#include <iostream>
using namespace std;

int find(int num[],int n,int key){
      int start = 0;
      int end = n-1;
      int mid = start+(end-start)/2;
      int ans = -1;
       while(start<=end){
           if(num[mid]==key){
              ans = mid;
              end = mid - 1 ;
           }else if (num[mid]>key){
               start = mid+1;
           }else{
               end = mid-1;
           }
           mid = start+(end-start)/2;
           return ans ;
       }
}

int last(int num[],int n,int key){
      int start = 0;
      int end = n-1;
      int mid = start+(end-start)/2;
      int ans = -1;
       while(start<=end){
           if(num[mid]==key){
              ans = mid;
               start= mid + 1 ;
           }else if (num[mid]>key){
               start = mid+1;
           }else{
               end = mid-1;
           }
           mid = start+(end-start)/2;
           return ans ;
       }
}

int main(){
     int n [6]={1,2,2,3,4,5};
    int key ;
    cout << "Enter a  key  : " << endl ;
    cin >> key;
    int index = find(n,6,key);
    cout<< "The first index is " << index  << endl ;
    cout << "The last index is " << last(n,6,key) << endl ; 

}