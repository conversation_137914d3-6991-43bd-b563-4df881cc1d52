#include<iostream>
using namespace std ; 
 
int main(){
     int n ;
     cout << "Enter a number of n" << endl ;
     cin >> n;
     int m ;
     cout <<"Enter a number of m" << endl ;
     cin >>m;

     int **arr =new int *[n];
      
     // make a one array 
     for(int i = 0 ;i<n ; i++){
          arr[i] =new int[m];
     }

     //take a input from a  user 
     for(int i = 0 ; i<n; i++){
        for(int j = 0 ; j <m ; j++){
             cin >> arr[i][j];
        }
     }

     //show a input 
     for(int i = 0 ; i<n ; i++){
         for(int j = 0 ; j< m; j++){
            cout << arr[i][j] << " ";
         }
         cout << endl ;
     }

}