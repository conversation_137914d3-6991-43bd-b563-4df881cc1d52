#include <iostream>
using namespace std;
int gcp(int a, int b)
{
   if(a==0){
      return b;
   }
   if(b==0){
     return a ;
   }
    while(a>b && b>a){
        if(a>b){
           a = a%b;
        }
        else{
             b= b%a;
        }
   }
   
}
int main()
{
    int a, b;
    cout << "Enter a value of a" << endl;
    cin >> a;
    cout << "Enter a value of b" << endl;
    cin >> b;
    int ans = gcp(a, b);
    cout << "The GCP Of two number is " << ans << endl;
}