#include<iostream>
using namespace std;
void destination(int start , int end){

    cout<< start << "to" << end << endl ;
    //base case 
    if(start== end){
        cout<<"I am  Reached" << endl;
        return;
    }
  //processing (means increament or decreament)
    start++;
    //recursive relation
    destination(start,end);


}


int main(){

   int start = 1;
   int end = 10 ; 

    destination(start,end);
    return 0 ;

}