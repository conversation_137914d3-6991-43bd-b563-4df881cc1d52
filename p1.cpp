#include <iostream>
#include <algorithm>
#include <vector>
#include <cmath>
#include <climits>
#include <unordered_map>
using namespace std;

// int main()
// {
//   int n;
//   cin >> n;
//   int ans = 0;

//   while (n != 0)
//   {
//     int bit = n & 1;        // 101->1 ==1 ;
//     ans = (ans * 10) + bit; // 0*10+1=1
//     n = n >> 1;             // n=0;
//   }
//   cout << ans;
//   return 0;
// }

int main(){
     int n;
     cout << "Enter value of n";
     cin >> n ; 
     string binary = "";
     while(n!=0){
          int bit = n&1;
          binary = to_string(bit)+binary;
          n = n>>1;
     }
     cout << binary ;
     return 0 ;

}
// int main()
// {
//   int n = 1010;
//   int ans = 0;
//   int i = 0;
//   while (n != 0)
//   {
//     int bit = n%10;
//     if (bit == 1)
//     {
//       ans = ans+(1<<i);
//     }
//     i++;
//     n = n/10;
//   }
//   cout << ans << endl;
//   return 0;
// }

// int main(){
//    int n=10;
//    int m=n;
//    int mask=0;
//    while(n!=0){
//        mask = (mask<<1)|1;
//        n=n>>1;
//    }
//    int ans=(~m)&mask;
//    cout<<ans<<endl;
//    return 0;
// }

/*Given an integer n, return true if it is a power of two. Otherwise, return false.

An integer n is a power of two, if there exists an integer x such that n == 2x.



Example 1:

Input: n = 1
Output: true
Explanation: 20 = 1
*/
// int main(){
//       int n =512;
//       bool found = false;
//       for(int i =0;i<=30;i++){
//         int ans = 1<<i;
//         if(ans==n){
//             found =true;
//             break ;
//         }
//       }
//       if(found){
//         cout << "true";
//       }else{
//         cout <<"false";
//       }
// }

// create a switch case
// int main(){
//     int a;
//     cout <<"Enter the number"<<endl;
//     cin>>a;
//    int ex;
//    cout<<"How many notes are needed";
//    cin>>ex;
//    switch(ex){
//        case 100:
//        case 50:
//        case 30:
//        case 20:
//        case 10:
//        case 1:{
//          int ans =a%ex;
//          int remain = a/ex;
//          cout<<remain<<endl;
//          cout<<ans<<endl;
//          break;
//        }
//        default:
//        cout<<"Invalid";
//    }
// }
/*
bool isprime(int n)
{
    if(n==1){
      return 0;
    }
  for (int i = 2; i < n; i++)
  {
    if (n % i == 0)
    {
      return 0;
    }
  }
  return 1;
}
int main()
{
  int n;
  cout << "Enter the number" << endl;
  cin >> n;
  if (isprime(n))
  {
    cout << "IS  prime " << endl;
  }
  else
  {
    cout << "IS not prime " << endl;
  }
}
  */
/*
int main(){

    std::vector<int> arr(100000 , 1 );
    for(int i =0; i<10;i++){
       cout << arr[i] << " ";
    }
}
*/
/*
int main(){
     int num[10];
     int size = sizeof(num)/sizeof(int); /// 10*4=40; 10= size while 4 is bytes; 40/4=10;
     cout << size << endl ;

}
*/

/*
int main(){
     std::vector<int>num(15 , 1 );
     for(int i =0 ; i<15 ; i++){
        cout << num[i] << " ";
     }


}
*/
// int getmax(int num[],int size){
//     int maxi = INT_MIN;
//     for(int i =0 ; i<size;i++){
//         maxi = max(maxi,num[i]);
//     }
//    return maxi ;
// }
// int sum(int num[],int size ){
//     int sum =0 ;
//     for(int i=0; i<size;i++){
//          sum+=num[i];
//     }
//     return sum;
// }
//  int main(){
//     int size ;
//     cout << "Enter size of array" << endl ;
//     cin >> size ;
//     int num[100];
//     for(int i =0 ; i<size ; i++){
//         cin >> num[i];
//     }
//     cout<<"The sum is" << sum(num,size) << endl;

//     // cout << "The maximum value is " << getmax(num,size) << endl;

//  }
// bool search(int num[],int size,int key){
//      for(int i =0 ; i<size;i++){
//          if(num[i]==key){
//            return 1;
//          }
//      }return 0;

// }
// int main(){
//   int num[5]={1,2,3,4,5};
//   cout<<"Check the value !"<< endl ;
//   cout <<"Enter the key value : " << endl ;
//   int key;
//   cin>> key ;
//     cout << search(num,5,key) << endl ;

// }
// REVERSE AN ARRAY
//  int reverse(int num[],int n){
//      int start = 0;
//      int end =4;

//      while(start<=end){
//        swap(num[start],num[end]);
//          start++ ;
//          end--;
//      }

//  }
// //  void printarray(int arr[],int size){
// //      for(int i=0 ;i<5;i++){
// //         cout << arr[i];
// //    }
// //  }
// int main(){
//    int num[5]={1,2,3,4,5};
//    reverse(num,5);
//    for(int i =0 ; i<5;i++)
//    {
//      cout << num[i] << " ";
//     }
// }

/* LEET CODE PROBLEM OF ARRAY */
/*
int reverse(int num[],int n  ){
   for(int i =0;i<n;i+=2){
     if(i+1<n){
      //  int temp = num[i];
      //  num[i]=num[i+1];
      //  num[i+1]= temp;
      swap(num[i],num[i+1]);
     }
   }
}
void printing(int n ,int num[]){
    for(int i =0 ; i<n;i++){
       cout << num[i]<< " ";
    }
}
int main(){
   int num[5]={4,7,2,8,3};


   reverse(num ,5);
   printing(5,num);


}
*/

// create a function ::-
/* int unique(int n[],int size){
   for(int i =0;i<size;i++){
        int count =0 ;
         for (int j=0;j<size;j++){
             if(n[i]==n[j]){
                count ++ ;
             }
         }
         if(count == 1 ){
          return n[i];
         }

   }
   return  -1 ;
}
   */
// find a unique number
//  int check(int num[],int size){
//       int ans = 0 ;
//      for(int i =0;i<size;i++){
//           ans = ans^num[i];
//      }
//      return ans ;
//  }
//  int main(){
//     int n[5]={1,2,3,2,1};
//     cout<<"The unique  number is "<< check(n,5) << endl ;

// }
// find occurences ::--
// int unique(int n[], int size){
//    map<int,int>freq;
//    for(int i:n)
//      freq[i]++;

//    set<int>freqset;
//    for(auto [key,value]:freq)
//         freqset.insert(value);
//        return freqset.size()==freq.size();

// }
// int main(){
//    int n[6]={1,2,1,1,2,3};
//    cout << unique(n,6) << endl ;

// }
// find a duplicate value
// int duplicate(int num[],int n ){
//     int ans=0;
//   for(int i=0;i<n;i++){
//        ans = ans^num[i];
//     }
//     for(int i=1;i<n;i++){
//        ans = ans^i;

//     }
//     return ans ;

// }
// int main(){
//      int n[5]={1,2,1,3,4};
//      cout << "The duplicate value is : " << duplicate(n,5) << endl ;

// }
// Triplet number
// vector<int> check(int n[], int size, int k) {
//     vector<int> ans;
//     unordered_map<int, int> num;

//     for (int i = 0; i < size; i++) {
//         num[n[i]]++;
//         if (num[n[i]] == k) {
//             ans.push_back(n[i]);
//         }
//     }

//     return ans;
// }

// int main() {
//     int n[5] = {1, 2, 3, 4, 5};
//     int k = 12;

//     vector<int> ans = check(n, 5, k);

//     for (int i : ans) {
//         cout << i << " ";
//     }

//     cout << endl;
//     return 0;
// }
/*  sort [0,1]*/
/*
void check(int num[],int n){
     int i =0 ;
     int j = n-1 ;
     while(num[i]==0 ){
         i++;
     }
     while(num[j]==1 ){
         j--;
     }
     if(i<j){
         swap(num[i],num[j]);
     }

}
void printarray(int num[],int n){
    for(int i =0 ; i<n;i++){
       cout << num[i] << " ";
    }
}
int main(){
  int n[6]={0,0,1,0,1,1};
  check(n,6);
  printarray(n,6);
}
  */
