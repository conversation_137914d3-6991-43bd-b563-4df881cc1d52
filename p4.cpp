#include <iostream>
using namespace std;

// Function to find first occurrence of key
int firstOccurrence(int num[], int n, int key) {
    int start = 0, end = n - 1, result = -1;
    while(start <= end) {
        int mid = start + (end - start)/2;
        if(num[mid] == key) {
            result = mid;
            end = mid - 1; // Search in left half
        } else if(num[mid] < key) {
            start = mid + 1;
        } else {
            end = mid - 1;
        }
    }
    return result;
}

// Function to find last occurrence of key
int lastOccurrence(int num[], int n, int key) {
    int start = 0, end = n - 1, result = -1;
    while(start <= end) {
        int mid = start + (end - start)/2;
        if(num[mid] == key) {
            result = mid;
            start = mid + 1; // Search in right half
        } else if(num[mid] < key) {
            start = mid + 1;
        } else {
            end = mid - 1;
        }
    }
    return result;
}

// Function to count occurrences
int countOccurrences(int num[], int n, int key) {
    int first = firstOccurrence(num, n, key);
    int last = lastOccurrence(num, n, key);
    if(first == -1 || last == -1) return 0;
    return last - first + 1;
}

int main() {
    int n[5] = {1, 2, 3, 3, 3};
    int key = 3;

    cout << "The occurrence of " << key << " is: " << countOccurrences(n, 5, key) << endl;
    return 0;
}
