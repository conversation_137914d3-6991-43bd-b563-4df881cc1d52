#include<iostream>
using namespace std;
int gcd(int m,int n){
    if(m==0){
        return n;
    }
   if(n==0){
    return m;
   }
   while(m!=n){
      if(m>n){
        m=m-n;
      }
      else{
        n=n-m;
      }
   }
   return m;
}

int main(){
    int m,n;
    cout<< "Enter the number of M and N"<< endl ;
    cin>>m >>n;

    int ans = gcd(m,n);
    cout<<"The gcd of 2 number is "<< ans << endl;



}