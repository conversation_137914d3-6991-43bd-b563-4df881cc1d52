#include <iostream>
using namespace std;
int getsum(int *arr, int n)
{
    int sum = 0;
    for (int i = 0; i < n; i++)
    {
        sum += arr[i];
    }
    return sum;
}

int main()
{
    int n;
    cout <<"Enter a size of array" << endl ;
    cin >> n;

    int *ptr = new int[n];
    cout << "Enter a value of element" << endl ;
    for (int i = 0; i < n; i++)
    {
        cin >> ptr[i];
    }
    int ans = getsum(ptr, n);
    cout << "The sum is : " << ans << endl;
}