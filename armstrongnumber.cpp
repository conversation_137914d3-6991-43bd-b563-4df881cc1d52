#include<iostream>
#include<cmath>
using namespace std;

bool isarmstrong(int n ){
     int cubes=1;
     int copy = n ;
     while(n!=0){
        int digit = n%10;
       cubes=pow(digit,3)+cubes;
        n = n/10;
    }
     return copy == cubes;
}
int main(){
    int n =153;

    if(isarmstrong(n)){
        cout<<"Yes!it is a armstrong number" << endl;
    }else{
         cout <<"No Sorry ! Check it " << endl ;
    }
}