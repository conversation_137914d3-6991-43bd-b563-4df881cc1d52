#include <iostream>
using namespace std;

bool ispossible(int arr[], int n, int m, int mid)
{
    int studentcount = 1;
    int page = 0;
    for (int i = 0; i < n; i++)
    {
        if (page + arr[i] <= mid)
        {
            page += arr[i];
        }
        else
        {
            studentcount++;
            if (studentcount > m || arr[i] > mid)
            {
                return false;
            }
            page = arr[i];
        }
    }
    return true;
}
int book(int arr[], int n, int m)
{
    int start = 0;
    int end = 0;
    for (int i = 0; i < n; i++)
    {
        end += arr[i];
    }
    int ans = -1;
    while (start <= end)
    {
        int mid = start + (end - start) / 2;
        if (ispossible(arr, n, m, mid))
        {
            ans = mid;
            end = mid - 1;
        }
        else
        {
            start = mid + 1;
        }
        mid = start + (end - start) / 2;
    }
    return ans;
}

int main()
{
    int num[4] = {10, 20, 30, 40};
    int n = 4;
    int m = 2;
    cout << book(num, n, m) << endl;
    return 0;
}