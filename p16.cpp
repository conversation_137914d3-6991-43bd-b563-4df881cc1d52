#include<iostream>
using namespace std;
char occur(string s){
   int arr[26] = {0};
   int n =s.length();
   for(int i =0;i<n;i++){
       int number = s[i] - 'a';
       arr[number]++;
   }
   int max = 0;
   int ans = 0;
   for(int i =0;i<26;i++){
       if(max<arr[i]){
            ans=i;
            max=arr[i];
        
            
       }
   }
   return  'a'+ans;
}
int main(){
    string s ;
    cin>> s;
    cout << "The maximum occurence is " << occur(s) << endl ;
}