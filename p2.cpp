#include<iostream>
using namespace std;
 
int check(int num[],int n,int key ){
     int start=0;
     int end = n-1;
     int mid=(start+end)/2;
     while(start<=end){
         if(num[mid]==key){
             return mid ; 
         }
         else if(num[mid]< key){
              start = mid +1 ;
         }
         else {
             end = mid - 1 ;
         }
         mid = (start+end-start)/2;
     }
   return -1;
}

int main(){
     int num[5]={2,4,6,8,10};
     int key;
     cout << "Enter the key " << " " << endl;
     cin >> key ;
     int index = check(num,5,key);
     if(index == -1){
        cout << " Key not found " << endl ;

     }else {
         cout << "Key is available " << endl ;
     }

}